@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 12% 8%;
    --foreground: 220 15% 95%;
    --border: 220 15% 18%;
    --input: 220 15% 15%;
    --ring: 204 100% 60%;
    --radius: 0.75rem;
  }

  * {
    border-color: #374151;
  }

  body {
    font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    background-color: #0f0f23;
    color: #f8fafc;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  }

  /* Custom scrollbar class */
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.6);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.8);
  }
}

@layer components {
  /* Glass effect */
  .glass-card {
    background: rgba(30, 30, 63, 0.7);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-button {
    background: rgba(99, 102, 241, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(99, 102, 241, 0.3);
  }

  .glass-button:hover {
    background: rgba(99, 102, 241, 0.9);
    border-color: rgba(99, 102, 241, 0.5);
  }

  /* Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  /* Grid background */
  .grid-bg {
    background-image:
      radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.15) 1px, transparent 0);
    background-size: 40px 40px;
  }

  /* Utility classes */
  .text-gradient {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .border-gradient {
    border: 1px solid;
    border-image: linear-gradient(135deg, #6366f1, #8b5cf6, #a855f7) 1;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.4);
  }

  /* Enhanced animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
  }

  @keyframes pulse-slow {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
  }

  @keyframes fade-in-right {
    0% {
      opacity: 0;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in-up {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }

  .animate-fade-in-right {
    animation: fade-in-right 0.6s ease-out;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  /* Delay classes */
  .delay-100 { animation-delay: 0.1s; }
  .delay-200 { animation-delay: 0.2s; }
  .delay-300 { animation-delay: 0.3s; }
  .delay-400 { animation-delay: 0.4s; }
  .delay-500 { animation-delay: 0.5s; }
}
