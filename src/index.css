@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 12% 8%;
    --foreground: 220 15% 95%;
    --border: 220 15% 18%;
    --input: 220 15% 15%;
    --ring: 204 100% 60%;
    --radius: 0.75rem;
  }

  * {
    border-color: #374151;
  }

  body {
    font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    background-color: #0f0f23;
    color: #f8fafc;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #6366f1;
  }
}

@layer components {
  /* Glass effect */
  .glass-card {
    background: rgba(30, 30, 63, 0.7);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-button {
    background: rgba(99, 102, 241, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(99, 102, 241, 0.3);
  }

  .glass-button:hover {
    background: rgba(99, 102, 241, 0.9);
    border-color: rgba(99, 102, 241, 0.5);
  }

  /* Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  /* Grid background */
  .grid-bg {
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.15) 1px, transparent 0);
    background-size: 40px 40px;
  }

  /* Utility classes */
  .text-gradient {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .border-gradient {
    border: 1px solid;
    border-image: linear-gradient(135deg, #6366f1, #8b5cf6, #a855f7) 1;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.4);
  }
}
