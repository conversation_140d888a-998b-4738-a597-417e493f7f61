import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Bo<PERSON>,
  Play,
  Pause,
  RotateCcw,
  Smile,
  User,
  Settings,
  Maximize2,
  Minimize2,
  X,
  Sparkles,
  Heart,
  Volume2,
} from 'lucide-react';

interface Live2DContainerProps {
  className?: string;
}

export function Live2DContainer({ className }: Live2DContainerProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isLoaded, setIsLoaded] = useState(true); // Mock state
  const [currentExpression, setCurrentExpression] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const expressions = ['Default', 'Happy', 'Excited', 'Shy', 'Surprised'];
  const currentModel = 'Lanhei';

  const handleExpressionChange = () => {
    setCurrentExpression((prev) => (prev + 1) % expressions.length);
  };

  if (!isVisible) return null;

  return (
    <div className={cn(
      "fixed bottom-6 right-6 z-50 w-80",
      isMinimized && "w-64",
      className
    )}>
      <Card className="glass-card border-0 shadow-glow overflow-hidden">
        
        {/* Header */}
        <div className="p-3 border-b border-gray-600/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-sm font-semibold text-white">{currentModel}</h3>
                <p className="text-xs text-gray-300">Live2D Assistant</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setIsMinimized(!isMinimized)}
              >
                {isMinimized ? (
                  <Maximize2 className="w-3 h-3" />
                ) : (
                  <Minimize2 className="w-3 h-3" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-6 w-6 p-0 text-red-400"
                onClick={() => setIsVisible(false)}
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        {!isMinimized && (
          <CardContent className="p-0">
            
            {/* Live2D Display Area */}
            <div className="relative h-64 bg-gradient-to-br from-purple-900/20 to-blue-900/20 overflow-hidden">
              
              {/* Background Effects */}
              <div className="absolute inset-0">
                <div className="absolute top-4 left-4 w-16 h-16 bg-purple-500/20 rounded-full blur-xl animate-pulse-slow" />
                <div className="absolute bottom-4 right-4 w-20 h-20 bg-blue-500/20 rounded-full blur-xl animate-pulse-slow" style={{animationDelay: '1s'}} />
              </div>

              {/* Model Display */}
              <div className="relative z-10 h-full flex items-center justify-center">
                {isLoaded ? (
                  <div className="text-center">
                    <div className="w-20 h-20 gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center shadow-glow animate-float">
                      <Bot className="w-10 h-10 text-white" />
                    </div>
                    <p className="text-white text-lg font-medium mb-1">{currentModel}</p>
                    <p className="text-gray-300 text-sm">{expressions[currentExpression]} Mode</p>
                  </div>
                ) : (
                  <div className="text-center text-gray-400">
                    <Bot className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Loading model...</p>
                  </div>
                )}
              </div>

              {/* Status Indicator */}
              <div className="absolute top-3 left-3">
                <Badge className={cn(
                  "text-xs",
                  isLoaded 
                    ? "bg-green-500/20 text-green-300 border-green-500/30" 
                    : "bg-gray-500/20 text-gray-300 border-gray-500/30"
                )}>
                  {isLoaded ? 'Ready' : 'Loading'}
                </Badge>
              </div>

              {/* Model Info */}
              <div className="absolute bottom-3 left-3">
                <Badge variant="outline" className="text-xs">
                  11 expressions
                </Badge>
              </div>
            </div>

            {/* Controls */}
            <div className="p-4 space-y-3">
              
              {/* Primary Actions */}
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExpressionChange}
                  disabled={!isLoaded}
                  className="text-xs"
                >
                  <Smile className="w-3 h-3 mr-1" />
                  Expression
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!isLoaded}
                  className="text-xs"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  Motion
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Reset
                </Button>
              </div>

              {/* Model Selection */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">Model:</span>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="text-xs">
                    <User className="w-3 h-3 mr-1" />
                    Switch
                  </Button>
                  <Button variant="outline" size="sm" className="text-xs">
                    <Settings className="w-3 h-3" />
                  </Button>
                </div>
              </div>

              {/* Interactive Features */}
              <div className="flex items-center justify-between pt-2 border-t border-gray-600/50">
                <div className="flex items-center space-x-3">
                  <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                    <Heart className="w-3 h-3 text-red-400" />
                  </Button>
                  <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                    <Volume2 className="w-3 h-3 text-blue-400" />
                  </Button>
                  <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                    <Play className="w-3 h-3 text-green-400" />
                  </Button>
                </div>
                
                <Badge variant="outline" className="text-xs">
                  Interactive Mode
                </Badge>
              </div>
            </div>
          </CardContent>
        )}

        {/* Minimized View */}
        {isMinimized && (
          <div className="p-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center animate-pulse-slow">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-white">{currentModel}</p>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-300">Active</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
