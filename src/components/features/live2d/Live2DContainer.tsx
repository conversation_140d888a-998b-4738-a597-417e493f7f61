import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Bo<PERSON>,
  Play,
  Pause,
  RotateCcw,
  Smile,
  User,
  Settings,
  Maximize2,
  Minimize2,
  X,
  Sparkles,
  Heart,
  Volume2,
} from 'lucide-react';

interface Live2DContainerProps {
  className?: string;
}

export function Live2DContainer({ className }: Live2DContainerProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isLoaded, setIsLoaded] = useState(true); // Mock state
  const [currentExpression, setCurrentExpression] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const expressions = ['Default', 'Happy', 'Excited', 'Shy', 'Surprised'];
  const currentModel = 'Lanhei';

  const handleExpressionChange = () => {
    setCurrentExpression((prev) => (prev + 1) % expressions.length);
  };

  if (!isVisible) return null;

  return (
    <div className={cn("w-full h-full flex flex-col", className)}>

      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-glow">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white">{currentModel}</h3>
            <p className="text-sm text-gray-300">Live2D Virtual Assistant</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => setIsMinimized(!isMinimized)}
          >
            {isMinimized ? (
              <Maximize2 className="w-4 h-4" />
            ) : (
              <Minimize2 className="w-4 h-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Live2D Display Area */}
      {!isMinimized && (
        <div className="flex-1 relative bg-gradient-to-br from-purple-900/30 to-cyan-900/30 rounded-2xl overflow-hidden border border-white/10">

          {/* Enhanced Background Effects */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-2xl animate-pulse-slow" />
            <div className="absolute bottom-1/3 right-1/4 w-32 h-32 bg-cyan-500/20 rounded-full blur-2xl animate-pulse-slow" style={{animationDelay: '1s'}} />
            <div className="absolute top-1/2 right-1/3 w-20 h-20 bg-pink-500/15 rounded-full blur-xl animate-pulse-slow" style={{animationDelay: '2s'}} />
          </div>

          {/* Model Display */}
          <div className="relative z-10 h-full flex items-center justify-center p-6">
            {isLoaded ? (
              <div className="text-center">
                <div className="w-32 h-32 gradient-primary rounded-full mx-auto mb-6 flex items-center justify-center shadow-glow-lg animate-float">
                  <Bot className="w-16 h-16 text-white" />
                </div>
                <h4 className="text-white text-xl font-bold mb-2">{currentModel}</h4>
                <p className="text-gray-300 text-lg mb-4">{expressions[currentExpression]} Mode</p>

                {/* Quick Stats */}
                <div className="flex justify-center space-x-4 text-sm">
                  <div className="text-center">
                    <div className="text-purple-400 font-bold">11</div>
                    <div className="text-gray-400">Expressions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-cyan-400 font-bold">8</div>
                    <div className="text-gray-400">Motions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-green-400 font-bold">Ready</div>
                    <div className="text-gray-400">Status</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400">
                <div className="w-24 h-24 border-4 border-gray-600 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-lg">Loading model...</p>
                <p className="text-sm text-gray-500 mt-2">Initializing Live2D engine</p>
              </div>
            )}
          </div>

          {/* Status Indicators */}
          <div className="absolute top-4 left-4">
            <Badge className={cn(
              "text-sm font-medium",
              isLoaded
                ? "bg-green-500/20 text-green-300 border-green-500/30 shadow-glow"
                : "bg-gray-500/20 text-gray-300 border-gray-500/30"
            )}>
              {isLoaded ? '● Ready' : '○ Loading'}
            </Badge>
          </div>

          {/* Model Info */}
          <div className="absolute bottom-4 left-4">
            <Badge variant="outline" className="text-sm bg-black/30 backdrop-blur-sm">
              Cubism 4.0
            </Badge>
          </div>

          {/* Performance Indicator */}
          <div className="absolute top-4 right-4">
            <Badge variant="outline" className="text-sm bg-black/30 backdrop-blur-sm">
              60 FPS
            </Badge>
          </div>
        </div>
      )}

      {/* Enhanced Controls */}
      {!isMinimized && (
        <div className="mt-6 space-y-4">

          {/* Primary Actions */}
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={handleExpressionChange}
              disabled={!isLoaded}
              className="glass-button border-purple-500/30 hover:border-purple-400/50"
            >
              <Smile className="w-4 h-4 mr-2" />
              Expression
            </Button>
            <Button
              variant="outline"
              disabled={!isLoaded}
              className="glass-button border-cyan-500/30 hover:border-cyan-400/50"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Motion
            </Button>
          </div>

          {/* Model Controls */}
          <div className="grid grid-cols-3 gap-2">
            <Button variant="outline" size="sm" className="text-sm">
              <User className="w-4 h-4 mr-1" />
              Switch
            </Button>
            <Button variant="outline" size="sm" className="text-sm">
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset
            </Button>
            <Button variant="outline" size="sm" className="text-sm">
              <Settings className="w-4 h-4 mr-1" />
              Config
            </Button>
          </div>

          {/* Interactive Features */}
          <div className="flex items-center justify-between p-3 bg-black/20 rounded-xl border border-white/10">
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" className="h-8 w-8 p-0 border-red-500/30 hover:border-red-400/50">
                <Heart className="w-4 h-4 text-red-400" />
              </Button>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0 border-blue-500/30 hover:border-blue-400/50">
                <Volume2 className="w-4 h-4 text-blue-400" />
              </Button>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0 border-green-500/30 hover:border-green-400/50">
                <Play className="w-4 h-4 text-green-400" />
              </Button>
            </div>

            <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30">
              Interactive
            </Badge>
          </div>
        </div>
      )}

      {/* Minimized View */}
      {isMinimized && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 gradient-primary rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse-slow shadow-glow">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <p className="text-sm font-medium text-white">{currentModel}</p>
            <div className="flex items-center justify-center space-x-2 mt-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-300">Active</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
