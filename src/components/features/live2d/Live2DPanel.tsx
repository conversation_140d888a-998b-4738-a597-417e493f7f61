import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import { cn } from '@/lib/utils';
import {
  Play,
  RotateCcw,
  Smile,
  User,
  Zap,
  Pause,
  Download,
  Settings,
  Eye,
  Heart,
  Sparkles,
  RefreshCw
} from 'lucide-react';
import type { Live2DModel, Live2DModelState } from '@/types/live2d';
import type { UseLive2DReturn } from '@/hooks/useLive2D';

interface Live2DPanelProps {
  width?: number;
  height?: number;
  className?: string;
  live2d: UseLive2DReturn;
}

export function Live2DPanel({
  width = 300,
  height = 400,
  className,
  live2d
}: Live2DPanelProps) {
  const {
    state,
    availableModels,
    currentModelExpressions,
    currentModelMotions,
    switchToModel,
    playExpression,
    playMotion,
    resetModel,
    toggleExpression,
    playRandomExpression,
    playRandomMotion
  } = live2d;

  const [isExpressionMenuOpen, setIsExpressionMenuOpen] = useState(false);
  const [isMotionMenuOpen, setIsMotionMenuOpen] = useState(false);

  // 处理模型切换
  const handleModelSwitch = async (modelId: string) => {
    try {
      await switchToModel(modelId);
    } catch (error) {
      console.error('模型切换失败:', error);
    }
  };

  // 处理表情播放
  const handleExpressionPlay = async (expressionId: string) => {
    try {
      await playExpression(expressionId);
      setIsExpressionMenuOpen(false);
    } catch (error) {
      console.error('表情播放失败:', error);
    }
  };

  // 处理动作播放
  const handleMotionPlay = async (motionGroup: string, motionIndex: number = 0) => {
    try {
      await playMotion(motionGroup, motionIndex);
      setIsMotionMenuOpen(false);
    } catch (error) {
      console.error('动作播放失败:', error);
    }
  };

  // 获取模型状态样式
  const getModelStatusStyle = () => {
    if (state.isLoading) {
      return 'border-orange-500/30 bg-orange-500/10 text-orange-400';
    }
    if (state.error) {
      return 'border-red-500/30 bg-red-500/10 text-red-400';
    }
    if (state.currentModel) {
      return 'border-green-500/30 bg-green-500/10 text-green-400';
    }
    return 'border-muted text-muted-foreground';
  };

  // 获取状态文本
  const getStatusText = () => {
    if (state.isLoading) return '加载中...';
    if (state.error) return `错误: ${state.error}`;
    if (state.currentModel) return state.currentModel.name;
    return '未加载模型';
  };

  return (
    <Card className={cn("border-green-500/20 bg-background/95 backdrop-blur-sm", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-green-400 font-mono text-sm uppercase tracking-wider">
            <User className="w-4 h-4 inline mr-2" />
            数字助手
          </CardTitle>

          {/* 操作按钮组 */}
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              className="h-6 px-2 text-xs"
              onClick={resetModel}
              disabled={!state.currentModel || state.isLoading}
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              重置
            </Button>

            <Button
              size="sm"
              variant="outline"
              className="h-6 px-2 text-xs"
              onClick={toggleExpression}
              disabled={!state.currentModel || state.isLoading || currentModelExpressions.length === 0}
            >
              <Smile className="w-3 h-3 mr-1" />
              表情
            </Button>

            <Button
              size="sm"
              variant="outline"
              className="h-6 px-2 text-xs"
              onClick={playRandomMotion}
              disabled={!state.currentModel || state.isLoading || Object.keys(currentModelMotions).length === 0}
            >
              <Zap className="w-3 h-3 mr-1" />
              动作
            </Button>
          </div>
        </div>

        {/* 模型选择器 */}
        <div className="mt-2 space-y-2">
          <div className="grid grid-cols-2 gap-1">
            {availableModels.map((model) => (
              <Button
                key={model.id}
                size="sm"
                variant={state.currentModel?.id === model.id ? "default" : "outline"}
                className="h-7 text-xs font-mono justify-start"
                onClick={() => handleModelSwitch(model.id)}
                disabled={state.isLoading}
              >
                {model.name}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* 模型显示区域 */}
        <div
          className={cn(
            "relative bg-gradient-to-br from-green-500/10 to-primary/5 rounded-lg overflow-hidden",
            "border-2 transition-all duration-300",
            getModelStatusStyle()
          )}
          style={{ width, height }}
        >
          {/* 加载指示器 */}
          {state.isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
              <LoadingSpinner variant="cyber" size="lg" message="加载模型中..." />
            </div>
          )}

          {/* 错误显示 */}
          {state.error && (
            <div className="absolute inset-0 flex flex-col items-center justify-center text-red-400 p-4 text-center z-10">
              <div className="text-4xl mb-2">⚠️</div>
              <div className="text-sm font-mono">{state.error}</div>
              <Button
                size="sm"
                variant="outline"
                className="mt-2"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                重试
              </Button>
            </div>
          )}

          {/* 模型信息覆盖层 */}
          {state.currentModel && (
            <>
              {/* 状态显示 */}
              <div className="absolute bottom-2 left-2 right-2">
                <Badge
                  variant="secondary"
                  className={cn(
                    "w-full justify-center text-xs font-mono",
                    state.currentModel && "bg-green-500/20 text-green-400"
                  )}
                >
                  {state.isPlaying && (
                    <Play className="w-3 h-3 mr-1 animate-pulse" />
                  )}
                  {getStatusText()}
                </Badge>
              </div>

              {/* 模型统计信息 */}
              <div className="absolute top-2 left-2 flex gap-1">
                {currentModelExpressions.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Smile className="w-3 h-3 mr-1" />
                    {currentModelExpressions.length}
                  </Badge>
                )}
                {Object.keys(currentModelMotions).length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Zap className="w-3 h-3 mr-1" />
                    {Object.values(currentModelMotions).flat().length}
                  </Badge>
                )}
              </div>

              {/* 当前表情显示 */}
              {state.currentExpression && (
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="text-xs animate-pulse">
                    <Sparkles className="w-3 h-3 mr-1" />
                    {state.currentExpression}
                  </Badge>
                </div>
              )}
            </>
          )}

          {/* 无模型时的占位符 */}
          {!state.currentModel && !state.isLoading && !state.error && (
            <div className="absolute inset-0 flex flex-col items-center justify-center text-muted-foreground">
              <div className="text-6xl mb-4 opacity-50">🤖</div>
              <div className="text-sm font-mono">选择模型</div>
              <div className="text-xs opacity-60 mt-1">从上方选择模型</div>
            </div>
          )}
        </div>

        {/* 控制面板 */}
        {state.currentModel && !state.isLoading && (
          <div className="p-4 space-y-3 border-t border-border/50">
            {/* 表情控制 */}
            {currentModelExpressions.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Smile className="w-4 h-4 text-green-400" />
                    <span className="text-sm font-mono text-green-400">表情</span>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={playRandomExpression}
                    >
                      <Sparkles className="w-3 h-3 mr-1" />
                      随机
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={() => setIsExpressionMenuOpen(!isExpressionMenuOpen)}
                    >
                      <Settings className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                {isExpressionMenuOpen && (
                  <div className="grid grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                    {currentModelExpressions.map((expression, index) => (
                      <Button
                        key={expression}
                        size="sm"
                        variant="outline"
                        className="h-7 text-xs justify-start"
                        onClick={() => handleExpressionPlay(expression)}
                        disabled={state.isPlaying}
                      >
                        <span className="w-4 text-center">{index + 1}</span>
                        {expression}
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* 动作控制 */}
            {Object.keys(currentModelMotions).length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4 text-orange-400" />
                    <span className="text-sm font-mono text-orange-400">动作</span>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={playRandomMotion}
                      disabled={state.isPlaying}
                    >
                      <Sparkles className="w-3 h-3 mr-1" />
                      随机
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={() => setIsMotionMenuOpen(!isMotionMenuOpen)}
                    >
                      <Settings className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                {isMotionMenuOpen && (
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {Object.entries(currentModelMotions).map(([group, motions]) => (
                      <div key={group} className="space-y-1">
                        <div className="text-xs text-muted-foreground font-mono uppercase">
                          {group}
                        </div>
                        <div className="grid grid-cols-2 gap-1">
                          {motions.map((motion, index) => (
                            <Button
                              key={`${group}-${index}`}
                              size="sm"
                              variant="outline"
                              className="h-7 text-xs justify-start"
                              onClick={() => handleMotionPlay(group, index)}
                              disabled={state.isPlaying}
                            >
                              <Play className="w-3 h-3 mr-1" />
                              {motion}
                            </Button>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* 状态信息 */}
            <div className="pt-2 border-t border-border/30">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>状态:</span>
                <div className="flex items-center gap-2">
                  {state.isPlaying && (
                    <>
                      <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" />
                      <span>播放中</span>
                    </>
                  )}
                  {!state.isPlaying && state.currentModel && (
                    <>
                      <div className="w-2 h-2 bg-green-400 rounded-full" />
                      <span>就绪</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 快捷键提示 */}
        <div className="p-2 border-t border-border/30 bg-muted/20">
          <div className="text-xs text-muted-foreground text-center font-mono">
            点击模型进行交互 • 空格键随机表情
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
