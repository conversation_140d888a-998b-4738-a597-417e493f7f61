import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Activity, Cpu, HardDrive, Wifi } from 'lucide-react';

interface StatusBarProps {
  className?: string;
}

export function StatusBar({ className }: StatusBarProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [cpuUsage] = useState(68); // Mock data
  const [memoryUsage] = useState(42); // Mock data

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <footer className={cn(
      "border-t border-border/40",
      "glass-card backdrop-blur-glass supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center justify-between text-sm">
          
          {/* Left Side - System Status */}
          <div className="flex items-center space-x-6">
            
            {/* System Status */}
            <div className="flex items-center space-x-2">
              <div className="status-dot status-online" />
              <span className="font-medium text-success">READY</span>
            </div>

            {/* CPU Usage */}
            <div className="hidden sm:flex items-center space-x-2">
              <Cpu className="w-4 h-4 text-muted-foreground" />
              <span className="text-muted-foreground">CPU</span>
              <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-primary rounded-full transition-all duration-300"
                  style={{ width: `${cpuUsage}%` }}
                />
              </div>
              <span className="text-xs font-mono text-foreground w-8 text-right">
                {cpuUsage}%
              </span>
            </div>

            {/* Memory Usage */}
            <div className="hidden md:flex items-center space-x-2">
              <HardDrive className="w-4 h-4 text-muted-foreground" />
              <span className="text-muted-foreground">MEM</span>
              <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-success rounded-full transition-all duration-300"
                  style={{ width: `${memoryUsage}%` }}
                />
              </div>
              <span className="text-xs font-mono text-foreground w-8 text-right">
                {memoryUsage}%
              </span>
            </div>

            {/* Connection Status */}
            <div className="hidden lg:flex items-center space-x-2">
              <Wifi className="w-4 h-4 text-muted-foreground" />
              <Badge variant="secondary" className="bg-destructive/10 text-destructive border-destructive/20 text-xs">
                WebSocket Disconnected
              </Badge>
            </div>
          </div>

          {/* Right Side - Time and Info */}
          <div className="flex items-center space-x-4">
            
            {/* Active Tasks */}
            <div className="hidden xl:flex items-center space-x-2">
              <Activity className="w-4 h-4 text-muted-foreground" />
              <span className="text-muted-foreground">Tasks: 0</span>
            </div>

            {/* Current Time */}
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground hidden sm:inline">Local Time:</span>
              <span className="font-mono text-foreground">
                {currentTime.toLocaleTimeString('zh-CN', {
                  hour12: false,
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                })}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 