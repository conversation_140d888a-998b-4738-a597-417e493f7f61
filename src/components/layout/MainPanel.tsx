import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import {
  Mic,
  MicOff,
  Play,
  Square,
  Pause,
  Volume2,
  VolumeX,
  AudioWaveform,
  Sparkles,
  Zap,
  Settings,
  Send,
  RotateCcw,
  MessageSquare,
  FileAudio,
} from 'lucide-react';

interface MainPanelProps {
  className?: string;
}

export function MainPanel({ className }: MainPanelProps) {
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);

  const maxLength = 5000;
  const remaining = maxLength - inputText.length;

  const handleSynthesize = () => {
    if (!inputText.trim()) return;
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      setIsPlaying(true);
    }, 3000);
  };

  return (
    <div className={cn("space-y-6", className)}>
      
      {/* Hero Section */}
      <Card className="glass-card border-0 shadow-glow">
        <CardHeader className="text-center pb-4">
          <div className="mb-4">
            <div className="w-16 h-16 gradient-primary rounded-2xl mx-auto flex items-center justify-center shadow-glow-lg mb-4">
              <AudioWaveform className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-3xl font-bold text-gradient mb-2">
              EVERCALL TTS
            </CardTitle>
            <p className="text-gray-300 text-lg">
              Next-generation AI-powered text-to-speech synthesis with Live2D virtual characters
            </p>
          </div>
        </CardHeader>
      </Card>

      {/* Text Input Section */}
      <Card className="glass-card border-0">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-purple-400" />
              <span className="text-lg">Text Input</span>
            </CardTitle>
            <Badge variant="outline" className={cn(
              "font-mono",
              remaining < 100 ? "border-red-400 text-red-400" : "border-gray-400 text-gray-400"
            )}>
              {remaining.toLocaleString()} characters remaining
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="Enter the text you want to convert to speech..."
            className="min-h-32 bg-black/20 border-gray-600 focus:border-purple-400 focus:ring-purple-400/50 resize-none text-gray-100 placeholder:text-gray-400"
            maxLength={maxLength}
          />
          <div className="text-sm text-gray-400">
            {inputText.length.toLocaleString()}/5,000
          </div>
        </CardContent>
      </Card>

      {/* Voice Recording Section */}
      <Card className="glass-card border-0">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mic className="w-5 h-5 text-red-400" />
            <span className="text-lg">Voice Recording</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button
            variant={isRecording ? "destructive" : "outline"}
            size="lg"
            onClick={() => setIsRecording(!isRecording)}
            className="w-full"
          >
            {isRecording ? (
              <>
                <MicOff className="w-5 h-5 mr-2" />
                Stop Recording
              </>
            ) : (
              <>
                <Mic className="w-5 h-5 mr-2" />
                Start Recording
              </>
            )}
          </Button>
          {isRecording && (
            <div className="mt-4 flex items-center space-x-2 text-red-400">
              <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Recording in progress...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Text Synthesis Section */}
      <Card className="glass-card border-0">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileAudio className="w-5 h-5 text-blue-400" />
            <span className="text-lg">Text Synthesis</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={handleSynthesize}
            disabled={!inputText.trim() || isProcessing}
            className="w-full gradient-primary text-white border-0 shadow-glow"
            size="lg"
          >
            {isProcessing ? (
              <>
                <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                Synthesize Speech
              </>
            ) : (
              <>
                <Send className="w-5 h-5 mr-2" />
                Synthesize Speech
              </>
            )}
          </Button>

          {isProcessing && (
            <div className="space-y-3">
              <div className="text-sm text-gray-300">Processing your request...</div>
              <Progress value={progress} className="w-full" />
              <div className="text-xs text-gray-400">
                Generating high-quality audio with AI voice synthesis
              </div>
            </div>
          )}

          {/* Audio Controls */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-600">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                disabled={!inputText}
              >
                {isPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsMuted(!isMuted)}
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
            </div>

            <div className="flex items-center space-x-1">
              <Badge variant={isPlaying ? "secondary" : "outline"} className="text-xs">
                {isPlaying ? 'Ready to play' : 'Standby'}
              </Badge>
            </div>

            <div className="flex items-center space-x-1">
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm">
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="glass-card border-0">
        <CardContent className="pt-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button variant="outline" className="text-sm">
              <Zap className="w-4 h-4 mr-1" />
              Quick TTS
            </Button>
            <Button variant="outline" className="text-sm">
              <Mic className="w-4 h-4 mr-1" />
              Voice Input
            </Button>
            <Button variant="outline" className="text-sm">
              <Settings className="w-4 h-4 mr-1" />
              Settings
            </Button>
            <Button variant="outline" className="text-sm">
              <Sparkles className="w-4 h-4 mr-1" />
              Effects
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
