import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import {
  Mic,
  MicOff,
  Play,
  Square,
  Pause,
  Volume2,
  VolumeX,
  AudioWaveform,
  Sparkles,
  Zap,
  Settings,
  Send,
  RotateCcw,
  MessageSquare,
  FileAudio,
} from 'lucide-react';

interface MainPanelProps {
  className?: string;
}

export function MainPanel({ className }: MainPanelProps) {
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);

  const maxLength = 5000;
  const remaining = maxLength - inputText.length;

  const handleSynthesize = () => {
    if (!inputText.trim()) return;
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      setIsPlaying(true);
    }, 3000);
  };

  return (
    <div className={cn("space-y-6", className)}>

      {/* Enhanced Hero Section */}
      <Card className="glass-card border-0 shadow-glow-lg border border-white/10 rounded-3xl overflow-hidden">
        <CardHeader className="text-center pb-6 relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-cyan-500/10 rounded-3xl"></div>

          <div className="relative z-10">
            <div className="w-20 h-20 gradient-primary rounded-3xl mx-auto flex items-center justify-center shadow-glow-lg mb-6 animate-float">
              <AudioWaveform className="w-10 h-10 text-white" />
            </div>
            <CardTitle className="text-4xl font-bold text-gradient mb-3">
              EVERCALL TTS
            </CardTitle>
            <p className="text-gray-300 text-xl max-w-2xl mx-auto leading-relaxed">
              Next-generation AI-powered text-to-speech synthesis with immersive Live2D virtual characters
            </p>

            {/* Feature highlights */}
            <div className="flex justify-center space-x-6 mt-6">
              <div className="text-center">
                <div className="text-purple-400 font-bold text-lg">AI-Powered</div>
                <div className="text-gray-400 text-sm">Neural TTS</div>
              </div>
              <div className="text-center">
                <div className="text-cyan-400 font-bold text-lg">Live2D</div>
                <div className="text-gray-400 text-sm">Virtual Avatar</div>
              </div>
              <div className="text-center">
                <div className="text-green-400 font-bold text-lg">Real-time</div>
                <div className="text-gray-400 text-sm">Processing</div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Text Input Section */}
      <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-3xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-500/20 rounded-xl flex items-center justify-center">
                <MessageSquare className="w-5 h-5 text-purple-400" />
              </div>
              <span className="text-xl font-bold">Text Input</span>
            </CardTitle>
            <Badge variant="outline" className={cn(
              "font-mono text-sm px-3 py-1",
              remaining < 100 ? "border-red-400 text-red-400 bg-red-500/10" : "border-gray-400 text-gray-400 bg-gray-500/10"
            )}>
              {remaining.toLocaleString()} remaining
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="relative">
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter the text you want to convert to speech... Support for multiple languages and advanced voice synthesis features."
              className="min-h-40 bg-black/30 border-gray-600 focus:border-purple-400 focus:ring-purple-400/50 resize-none text-gray-100 placeholder:text-gray-400 rounded-2xl text-lg leading-relaxed backdrop-blur-sm"
              maxLength={maxLength}
            />

            {/* Character count overlay */}
            <div className="absolute bottom-4 right-4 flex items-center space-x-2">
              <Badge variant="outline" className="bg-black/50 backdrop-blur-sm">
                {inputText.length.toLocaleString()}/5,000
              </Badge>
            </div>
          </div>

          {/* Quick text suggestions */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setInputText("Hello! Welcome to EVERCALL TTS system. I'm your virtual assistant.")}
              className="text-xs bg-purple-500/10 border-purple-500/30 hover:bg-purple-500/20"
            >
              Sample Text 1
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setInputText("This is a demonstration of advanced AI-powered text-to-speech technology with Live2D character integration.")}
              className="text-xs bg-cyan-500/10 border-cyan-500/30 hover:bg-cyan-500/20"
            >
              Sample Text 2
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setInputText("")}
              className="text-xs bg-gray-500/10 border-gray-500/30 hover:bg-gray-500/20"
            >
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Voice Recording & Synthesis Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

        {/* Voice Recording */}
        <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-3xl">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-500/20 rounded-xl flex items-center justify-center">
                <Mic className="w-5 h-5 text-red-400" />
              </div>
              <span className="text-xl font-bold">Voice Input</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              variant={isRecording ? "destructive" : "outline"}
              size="lg"
              onClick={() => setIsRecording(!isRecording)}
              className={cn(
                "w-full h-16 text-lg font-semibold rounded-2xl transition-all duration-300",
                isRecording
                  ? "bg-red-500/20 border-red-400 text-red-300 shadow-glow animate-pulse"
                  : "glass-button border-red-500/30 hover:border-red-400/50"
              )}
            >
              {isRecording ? (
                <>
                  <MicOff className="w-6 h-6 mr-3" />
                  Stop Recording
                </>
              ) : (
                <>
                  <Mic className="w-6 h-6 mr-3" />
                  Start Recording
                </>
              )}
            </Button>

            {isRecording && (
              <div className="p-4 bg-red-500/10 rounded-2xl border border-red-500/30">
                <div className="flex items-center space-x-3 text-red-400">
                  <div className="w-4 h-4 bg-red-400 rounded-full animate-pulse"></div>
                  <span className="font-medium">Recording in progress...</span>
                </div>
                <div className="mt-2 text-sm text-gray-400">
                  Speak clearly for best results
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Text Synthesis */}
        <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-3xl">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500/20 rounded-xl flex items-center justify-center">
                <FileAudio className="w-5 h-5 text-blue-400" />
              </div>
              <span className="text-xl font-bold">Speech Synthesis</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleSynthesize}
              disabled={!inputText.trim() || isProcessing}
              className={cn(
                "w-full h-16 text-lg font-semibold rounded-2xl transition-all duration-300",
                "gradient-primary text-white border-0 shadow-glow-lg hover:shadow-glow-lg",
                isProcessing && "animate-pulse"
              )}
              size="lg"
            >
              {isProcessing ? (
                <>
                  <Sparkles className="w-6 h-6 mr-3 animate-spin" />
                  Synthesizing...
                </>
              ) : (
                <>
                  <Send className="w-6 h-6 mr-3" />
                  Generate Speech
                </>
              )}
            </Button>

            {isProcessing && (
              <div className="space-y-3 p-4 bg-blue-500/10 rounded-2xl border border-blue-500/30">
                <div className="text-sm text-blue-300 font-medium">Processing your request...</div>
                <Progress value={progress} className="w-full h-2" />
                <div className="text-xs text-gray-400">
                  Generating high-quality audio with AI voice synthesis
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Audio Controls */}
      <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-3xl">
        <CardHeader>
          <CardTitle className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-500/20 rounded-xl flex items-center justify-center">
              <Volume2 className="w-5 h-5 text-green-400" />
            </div>
            <span className="text-xl font-bold">Audio Controls</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">

          {/* Main Audio Controls */}
          <div className="flex items-center justify-between p-4 bg-black/20 rounded-2xl border border-white/10">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="lg"
                onClick={() => setIsPlaying(!isPlaying)}
                disabled={!inputText}
                className="h-12 w-12 p-0 glass-button border-green-500/30 hover:border-green-400/50"
              >
                {isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => setIsMuted(!isMuted)}
                className="h-12 w-12 p-0 glass-button border-blue-500/30 hover:border-blue-400/50"
              >
                {isMuted ? (
                  <VolumeX className="w-6 h-6" />
                ) : (
                  <Volume2 className="w-6 h-6" />
                )}
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              <Badge variant={isPlaying ? "secondary" : "outline"} className="text-sm px-3 py-1">
                {isPlaying ? '● Playing' : '○ Standby'}
              </Badge>

              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" className="glass-button">
                  <Settings className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" className="glass-button">
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Actions Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button variant="outline" className="h-12 glass-button border-yellow-500/30 hover:border-yellow-400/50">
              <Zap className="w-5 h-5 mr-2" />
              Quick TTS
            </Button>
            <Button variant="outline" className="h-12 glass-button border-red-500/30 hover:border-red-400/50">
              <Mic className="w-5 h-5 mr-2" />
              Voice Input
            </Button>
            <Button variant="outline" className="h-12 glass-button border-purple-500/30 hover:border-purple-400/50">
              <Settings className="w-5 h-5 mr-2" />
              Settings
            </Button>
            <Button variant="outline" className="h-12 glass-button border-pink-500/30 hover:border-pink-400/50">
              <Sparkles className="w-5 h-5 mr-2" />
              Effects
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
