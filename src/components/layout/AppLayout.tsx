import React from 'react';
import { cn } from '@/lib/utils';
import { MainPanel } from './MainPanel';
import { InfoPanel } from './InfoPanel';
import { Live2DContainer } from '@/components/features/live2d/Live2DContainer';
import { Header } from './Header';
import { StatusBar } from './StatusBar';

interface AppLayoutProps {
  className?: string;
}

export function AppLayout({ className }: AppLayoutProps) {
  return (
    <div className={cn(
      "min-h-screen flex flex-col",
      className
    )}>
      {/* Header Navigation */}
      <Header />

      {/* Main Content Area */}
      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-6 h-full min-h-[calc(100vh-12rem)]">
          
          {/* Left Panel - Main TTS Interface */}
          <div className="xl:col-span-8 space-y-6">
            <div className="animate-fade-in-up">
              <MainPanel />
            </div>
          </div>

          {/* Right Panel - Side Information */}
          <div className="xl:col-span-4 space-y-6">
            <div className="animate-fade-in-up delay-200">
              <InfoPanel />
            </div>
          </div>
        </div>
      </main>

      {/* Live2D Character - Floating */}
      <div className="fixed bottom-6 right-6 z-40 animate-fade-in-right delay-500">
        <Live2DContainer />
      </div>

      {/* Status Bar */}
      <StatusBar />
    </div>
  );
}
