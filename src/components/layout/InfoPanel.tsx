import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import {
  Activity,
  Database,
  Headphones,
  Wifi,
  WifiOff,
  User,
  Bot,
  Trash2,
  Play,
  Keyboard,
  Zap,
  RefreshCw,
  Clock,
  HardDrive,
  Cpu,
  BarChart3,
} from 'lucide-react';

interface InfoPanelProps {
  className?: string;
}

export function InfoPanel({ className }: InfoPanelProps) {
  // Mock data for demonstration
  const audioCache = [
    { id: '1', text: 'Hello, welcome to EVERCALL TTS system!', duration: '00:03', size: '48KB', timestamp: '14:32' },
    { id: '2', text: 'This is an example of text-to-speech conversion.', duration: '00:05', size: '62KB', timestamp: '14:28' },
    { id: '3', text: 'AI voice synthesis technology demonstration.', duration: '00:04', size: '55KB', timestamp: '14:25' },
  ];

  const systemStats = [
    { label: 'CPU Usage', value: 68, unit: '%', color: 'text-blue-400' },
    { label: 'Memory', value: 42, unit: '%', color: 'text-green-400' },
    { label: 'Storage', value: 23, unit: '%', color: 'text-purple-400' },
  ];

  const shortcuts = [
    { key: '1-9, 0', action: 'Quick playback' },
    { key: 'Ctrl+Enter', action: 'Synthesize' },
    { key: 'Space', action: 'Play/Pause' },
    { key: 'R', action: 'Record voice' },
  ];

  return (
    <div className={cn("space-y-4", className)}>

      {/* Compact System Status */}
      <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-2xl">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Activity className="w-5 h-5 text-green-400" />
            <span>System Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">

          {/* Connection Status */}
          <div className="flex items-center justify-between p-2 bg-green-500/10 rounded-xl border border-green-500/20">
            <div className="flex items-center space-x-2">
              <Wifi className="w-4 h-4 text-green-400" />
              <span className="text-sm font-medium">WebSocket</span>
            </div>
            <Badge className="bg-green-500/20 text-green-300 border-green-500/30 text-xs">
              Connected
            </Badge>
          </div>

          {/* Live2D Status */}
          <div className="flex items-center justify-between p-2 bg-blue-500/10 rounded-xl border border-blue-500/20">
            <div className="flex items-center space-x-2">
              <Bot className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium">Live2D</span>
            </div>
            <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
              Ready
            </Badge>
          </div>

          {/* Compact System Performance */}
          <div className="space-y-2">
            {systemStats.map((stat, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {stat.label === 'CPU Usage' && <Cpu className="w-3 h-3 text-gray-400" />}
                  {stat.label === 'Memory' && <BarChart3 className="w-3 h-3 text-gray-400" />}
                  {stat.label === 'Storage' && <HardDrive className="w-3 h-3 text-gray-400" />}
                  <span className="text-xs text-gray-300">{stat.label}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-12 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className={cn("h-full transition-all duration-300", {
                        'bg-blue-400': stat.label === 'CPU Usage',
                        'bg-green-400': stat.label === 'Memory',
                        'bg-purple-400': stat.label === 'Storage',
                      })}
                      style={{ width: `${stat.value}%` }}
                    />
                  </div>
                  <span className={cn("text-xs font-mono", stat.color)}>
                    {stat.value}{stat.unit}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Compact Audio Cache */}
      <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-2xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-lg">
              <Headphones className="w-5 h-5 text-orange-400" />
              <span>Audio Cache</span>
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              {audioCache.length}/10
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-32">
            <div className="space-y-1">
              {audioCache.map((item, index) => (
                <div
                  key={item.id}
                  className="p-2 bg-black/20 rounded-lg border border-gray-600 hover:border-orange-400/50 transition-colors cursor-pointer group"
                >
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-gray-200 truncate mb-1">
                        {item.text}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-gray-400">
                        <span>{item.timestamp}</span>
                        <span>{item.size}</span>
                        <Badge variant="outline" className="text-xs h-4">
                          {index === 9 ? '0' : (index + 1).toString()}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button variant="outline" size="sm" className="h-5 w-5 p-0">
                        <Play className="w-2 h-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Combined Shortcuts & Actions */}
      <Card className="glass-card border-0 shadow-glow border border-white/10 rounded-2xl">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Keyboard className="w-5 h-5 text-purple-400" />
            <span>Shortcuts & Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">

          {/* Compact Shortcuts */}
          <div className="space-y-2">
            {shortcuts.slice(0, 3).map((shortcut, index) => (
              <div key={index} className="flex items-center justify-between">
                <Badge variant="outline" className="font-mono text-xs">
                  {shortcut.key}
                </Badge>
                <span className="text-xs text-gray-300">{shortcut.action}</span>
              </div>
            ))}
          </div>

          {/* Quick Actions Grid */}
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" size="sm" className="text-xs h-8 glass-button">
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-8 glass-button">
              <Trash2 className="w-3 h-3 mr-1" />
              Clear
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-8 glass-button">
              <User className="w-3 h-3 mr-1" />
              Profile
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-8 glass-button">
              <Database className="w-3 h-3 mr-1" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
