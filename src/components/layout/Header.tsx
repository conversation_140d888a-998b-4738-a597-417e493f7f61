import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Zap, Wifi, Settings, User } from 'lucide-react';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  return (
    <header className={cn(
      "sticky top-0 z-50 border-b border-border/40",
      "glass-card backdrop-blur-glass supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-lg">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div className="absolute -inset-1 bg-gradient-primary rounded-xl blur opacity-20 -z-10" />
            </div>
            
            <div className="flex flex-col">
              <h1 className="text-xl font-bold text-gradient-primary">
                EVERCALL
              </h1>
              <p className="text-xs text-muted-foreground font-medium">
                TTS Terminal System
              </p>
            </div>
          </div>

          {/* Status and Actions */}
          <div className="flex items-center space-x-4">
            
            {/* Connection Status */}
            <div className="hidden sm:flex items-center space-x-2">
              <div className="status-dot status-online" />
              <span className="text-sm font-medium text-foreground">
                System Online
              </span>
            </div>

            {/* WebSocket Status */}
            <Badge 
              variant="secondary" 
              className="hidden md:flex items-center gap-1.5 bg-destructive/10 text-destructive border-destructive/20"
            >
              <Wifi className="w-3 h-3" />
              Disconnected
            </Badge>

            {/* Live2D Status */}
            <Badge 
              variant="secondary" 
              className="hidden lg:flex items-center gap-1.5 bg-success/10 text-success border-success/20"
            >
              <User className="w-3 h-3" />
              Live2D Ready
            </Badge>

            {/* Settings Button */}
            <button 
              className="w-8 h-8 rounded-lg bg-muted hover:bg-muted/80 flex items-center justify-center transition-colors interactive"
              aria-label="Settings"
            >
              <Settings className="w-4 h-4 text-muted-foreground" />
            </button>

            {/* Version */}
            <div className="hidden xl:block text-xs text-muted-foreground font-mono">
              v2.1.0
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 