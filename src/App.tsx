import React from 'react';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { Toaster } from '@/components/ui/toaster';
import { MainPanel } from '@/components/layout/MainPanel';
import { InfoPanel } from '@/components/layout/InfoPanel';
import { Live2DContainer } from '@/components/features/live2d/Live2DContainer';

function App() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
        {/* Animated background */}
        <div className="fixed inset-0 -z-10">
          {/* Grid pattern */}
          <div className="absolute inset-0 grid-bg opacity-20" />
          
          {/* Floating orbs */}
          <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-float" />
          <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}} />
          <div className="absolute top-1/2 left-3/4 w-64 h-64 bg-indigo-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '4s'}} />
        </div>

        {/* Header */}
        <header className="relative z-10 p-6">
          <div className="max-w-7xl mx-auto">
            <div className="glass-card rounded-2xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center shadow-glow">
                    <span className="text-white font-bold text-xl">E</span>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gradient">EVERCALL</h1>
                    <p className="text-sm text-gray-300">TTS Terminal System</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full border border-green-500/30">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-300 text-sm font-medium">System Online</span>
                  </div>
                  <div className="text-sm text-gray-400">v2.1.0</div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="relative z-10 px-6 pb-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              
              {/* Main Panel - Takes 2 columns on xl screens */}
              <div className="xl:col-span-2">
                <MainPanel />
              </div>
              
              {/* Side Panel - Takes 1 column on xl screens */}
              <div className="space-y-6">
                <InfoPanel />
              </div>
            </div>
          </div>
        </main>

        {/* Live2D Character - Floating */}
        <Live2DContainer />
        
        {/* Toast notifications */}
        <Toaster />
      </div>
    </ErrorBoundary>
  );
}

export default App;