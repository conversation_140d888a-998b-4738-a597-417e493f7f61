import React from 'react';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { Toaster } from '@/components/ui/toaster';
import { MainPanel } from '@/components/layout/MainPanel';
import { InfoPanel } from '@/components/layout/InfoPanel';
import { Live2DContainer } from '@/components/features/live2d/Live2DContainer';

function App() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-purple-950 to-indigo-950 text-white overflow-hidden">
        {/* Enhanced Animated Background */}
        <div className="fixed inset-0 -z-10">
          {/* Layered grid patterns */}
          <div className="absolute inset-0 grid-bg opacity-30" />
          <div className="absolute inset-0 grid-bg opacity-10" style={{
            backgroundSize: '80px 80px',
            transform: 'rotate(45deg) scale(1.5)'
          }} />

          {/* Enhanced floating orbs with better positioning */}
          <div className="absolute top-1/6 left-1/6 w-96 h-96 bg-purple-500/15 rounded-full blur-3xl animate-float" />
          <div className="absolute top-2/3 right-1/5 w-80 h-80 bg-cyan-500/15 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}} />
          <div className="absolute top-1/3 left-2/3 w-72 h-72 bg-indigo-500/15 rounded-full blur-3xl animate-float" style={{animationDelay: '4s'}} />
          <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl animate-float" style={{animationDelay: '6s'}} />

          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
        </div>

        {/* Redesigned Header */}
        <header className="relative z-10 p-4 lg:p-6">
          <div className="max-w-[1920px] mx-auto">
            <div className="glass-card rounded-3xl p-6 shadow-glow-lg border border-white/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <div className="relative">
                    <div className="w-16 h-16 gradient-primary rounded-2xl flex items-center justify-center shadow-glow-lg">
                      <span className="text-white font-bold text-2xl">E</span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse shadow-glow"></div>
                  </div>
                  <div>
                    <h1 className="text-3xl lg:text-4xl font-bold text-gradient mb-1">EVERCALL</h1>
                    <p className="text-gray-300 text-lg">AI-Powered TTS Terminal System</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-green-500/20 rounded-full border border-green-500/30 shadow-glow">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-300 font-medium">System Online</span>
                  </div>
                  <div className="text-gray-400 font-mono">v2.1.0</div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Redesigned Main Content - Split Screen Layout */}
        <main className="relative z-10 px-4 lg:px-6 pb-6">
          <div className="max-w-[1920px] mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 h-[calc(100vh-200px)]">

              {/* Left Panel - Main Controls */}
              <div className="lg:col-span-7 xl:col-span-8 space-y-6 overflow-y-auto custom-scrollbar">
                <MainPanel />
              </div>

              {/* Right Panel - Live2D Character & Info */}
              <div className="lg:col-span-5 xl:col-span-4 space-y-6">
                {/* Live2D Character Display - Now integrated into layout */}
                <div className="glass-card rounded-3xl p-6 shadow-glow-lg border border-white/10 h-[60%] min-h-[400px]">
                  <Live2DContainer className="relative w-full h-full" />
                </div>

                {/* Info Panel - Compact version */}
                <div className="h-[40%] overflow-y-auto custom-scrollbar">
                  <InfoPanel />
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Toast notifications */}
        <Toaster />
      </div>
    </ErrorBoundary>
  );
}

export default App;
